# Hero Section Background Video Implementation

## Overview
The HeroSection component has been updated to use a background video instead of a solid background color (`bg-primary`). This creates a more engaging and dynamic hero section while maintaining all existing functionality and content positioning.

## Implementation Details

### Video Configuration
- **Video URL**: `https://res.cloudinary.com/dfcsaxtru/video/upload/v1754394418/HERO_PAGE_1_yvuicp.mp4`
- **Autoplay**: Enabled for immediate engagement
- **Loop**: Continuous playback for seamless experience
- **Muted**: Default muted state for better UX and browser compatibility
- **Preload**: Set to "metadata" for optimal loading performance

### Technical Structure

#### 1. Container Setup
```tsx
<section className="relative h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 overflow-hidden">
```
- Added `relative` positioning for absolute child elements
- Added `overflow-hidden` to prevent video overflow
- Removed `bg-primary` background color

#### 2. Background Video Element
```tsx
<video
  className="absolute inset-0 w-full h-full object-cover z-0"
  autoPlay
  loop
  muted
  playsInline
  preload="metadata"
  style={{ backgroundColor: '#0e0e0a' }}
>
```
- Positioned absolutely to cover entire section
- `object-cover` ensures video covers full area while maintaining aspect ratio
- `z-0` places video behind all content
- Fallback background color matches original `bg-primary` color

#### 3. Overlay for Text Readability
```tsx
<div className="absolute inset-0 bg-black/30 z-10"></div>
```
- Semi-transparent black overlay ensures text remains readable
- `z-10` places overlay above video but below content

#### 4. Content Layer
```tsx
<div className="relative z-20 max-w-4xl mx-auto text-center">
```
- `z-20` ensures content appears above video and overlay
- All existing content and styling preserved

## Browser Compatibility

### Video Attributes
- `playsInline`: Prevents fullscreen on mobile devices
- `preload="metadata"`: Loads video metadata without full video download
- Fallback text for unsupported browsers

### Responsive Design
- Video automatically scales to container size
- `object-cover` maintains aspect ratio across all screen sizes
- Existing responsive classes preserved for content

## Performance Considerations

### Loading Strategy
- `preload="metadata"` reduces initial bandwidth usage
- Video hosted on Cloudinary CDN for optimal delivery
- Fallback background color provides immediate visual feedback

### Mobile Optimization
- `playsInline` attribute prevents unwanted fullscreen behavior
- Muted autoplay complies with mobile browser policies
- Responsive design ensures proper scaling on all devices

## Accessibility

### User Experience
- Muted by default to avoid unexpected audio
- No flashing or rapid motion that could trigger seizures
- Maintains keyboard navigation for interactive elements

### Fallback Support
- Background color fallback for browsers without video support
- Descriptive fallback text for screen readers
- All existing ARIA labels and semantic structure preserved

## Maintenance

### Video Updates
To update the background video:
1. Replace the `src` URL in the `<source>` element
2. Ensure new video maintains similar aspect ratio
3. Test across different screen sizes and browsers

### Styling Adjustments
- Modify overlay opacity by changing `bg-black/30` class
- Adjust fallback color in the `style` attribute
- Update z-index values if layering conflicts occur

## Testing Checklist
- [ ] Video loads and plays automatically
- [ ] Content remains readable over video
- [ ] Responsive behavior on mobile/tablet/desktop
- [ ] Fallback works when video fails to load
- [ ] Performance acceptable on slower connections
- [ ] Accessibility features function correctly
